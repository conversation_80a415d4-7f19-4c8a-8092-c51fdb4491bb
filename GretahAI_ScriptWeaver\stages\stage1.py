"""
Stage 1: Navigation Hub and CSV Upload

This module serves as both the central navigation hub and CSV/Excel file upload functionality.
It provides:
- Central navigation hub with mode selection (Script Generation vs Script Playground)
- File upload and validation
- Test case parsing and processing
- Data preview and verification
- Routing to appropriate workflow stages based on user choice

This combines the navigation hub functionality with CSV upload in a single stage.
"""

import os
import logging
import tempfile
import streamlit as st
import pandas as pd
from pathlib import Path
from state_manager import StateStage

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage1")

# Import helper functions from other modules
from core.excel_parser import parse_excel
from debug_utils import debug

def validate_uploaded_file(uploaded_file, file_content):
    """
    Validate uploaded file before processing.

    Args:
        uploaded_file: Streamlit uploaded file object
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of validation errors (empty if valid)
    """
    errors = []

    # File size validation (50MB limit)
    if len(file_content) == 0:
        errors.append("File is empty")
    elif len(file_content) > 50 * 1024 * 1024:
        errors.append("File too large (max 50MB)")

    # File extension validation
    if not uploaded_file.name.lower().endswith('.xlsx'):
        errors.append("Invalid file extension (must be .xlsx)")

    # Basic Excel file signature check
    if len(file_content) >= 2 and not file_content.startswith(b'PK'):
        errors.append("File does not appear to be a valid Excel file")

    return errors

def safe_get_test_case_count(test_cases):
    """
    Safely get test case count with validation.

    Args:
        test_cases: Test cases data structure

    Returns:
        int: Number of test cases (0 if invalid)
    """
    if not test_cases:
        return 0
    if not isinstance(test_cases, list):
        debug(f"Warning: test_cases is not a list, type: {type(test_cases)}")
        return 0
    return len(test_cases)

def validate_stage1_completion(state):
    """
    Validate Stage 1 completion criteria.

    Args:
        state: StateManager instance

    Returns:
        tuple: (is_valid: bool, message: str)
    """
    if not hasattr(state, 'test_cases') or not state.test_cases:
        return False, "No test cases loaded"

    if not hasattr(state, 'uploaded_excel') or not state.uploaded_excel:
        return False, "No Excel file uploaded"

    if not os.path.exists(state.uploaded_excel):
        return False, "Uploaded file no longer exists"

    return True, "Stage 1 completed successfully"

@st.cache_data
def parse_excel_cached(file_content):
    """
    Cached version of parse_excel function with improved resource management.

    Args:
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of test cases
    """
    debug("Using cached parse_excel function")
    temp_file_path = None

    try:
        # Create a temporary file to pass to parse_excel
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Parse the Excel file using the existing function
        test_cases = parse_excel(temp_file_path)
        return test_cases

    except Exception as e:
        debug(f"Error in cached parse_excel: {e}")
        logger.error(f"Error in cached parse_excel: {e}")
        raise
    finally:
        # Ensure cleanup even if parsing fails
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as cleanup_error:
                debug(f"Warning: Failed to cleanup temp file {temp_file_path}: {cleanup_error}")

def stage1_upload_excel(state):
    """Stage 1: Navigation Hub and CSV Upload."""
    # Professional header with clean typography
    st.markdown("""
    <div style="margin-bottom: 2rem;">
        <h1 style="color: #1f2937; font-size: 2.5rem; font-weight: 600; margin-bottom: 0.5rem; letter-spacing: -0.025em;">
            GretahAI ScriptWeaver
        </h1>
        <p style="color: #6b7280; font-size: 1.125rem; margin-bottom: 0; font-weight: 400;">
            Automated Test Script Generation Platform
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Navigation Hub Section
    _render_navigation_hub(state)

    # Only show file upload section if in Script Generation mode or if test cases are needed
    _render_file_upload_section(state)

def _process_uploaded_file_stage1(state, uploaded_file):
    """Process the uploaded Excel file in Stage 1."""
    try:
        # Get the file content
        file_content = uploaded_file.getvalue()

        # Validate uploaded file before processing
        validation_errors = validate_uploaded_file(uploaded_file, file_content)
        if validation_errors:
            for error in validation_errors:
                st.error(f"❌ {error}")
            return

        # Check if this is the same file we've already processed
        current_hash = hash(file_content)
        if hasattr(state, 'last_file_content_hash') and state.last_file_content_hash == current_hash:
            debug("File content unchanged - skipping reprocessing")
            logger.info("File content unchanged - skipping reprocessing")
            st.success(f"✅ File already processed: {uploaded_file.name}")
        else:
            debug("New or changed file detected - processing")
            logger.info("New or changed file detected - processing")

            # Update the content hash in state
            old_hash = getattr(state, 'last_file_content_hash', None)
            state.last_file_content_hash = current_hash
            debug(f"State change: last_file_content_hash = {current_hash} (was: {old_hash})")

            # Save the uploaded file to a temporary location
            temp_dir = Path("temp_uploads")
            temp_dir.mkdir(exist_ok=True)

            # Use a consistent filename based on the uploaded file name
            safe_filename = ''.join(c if c.isalnum() else '_' for c in uploaded_file.name)
            temp_file_path = temp_dir / f"test_cases_{safe_filename}"

            with open(temp_file_path, "wb") as f:
                f.write(file_content)

            # Update state with file information
            old_excel_path = getattr(state, 'uploaded_excel', None)
            state.uploaded_excel = str(temp_file_path)
            state.uploaded_file = str(temp_file_path)  # Backward compatibility
            debug(f"State change: uploaded_excel = {state.uploaded_excel} (was: {old_excel_path})")

            # Process file results
            _display_processing_results_stage1(state, uploaded_file, file_content, temp_file_path)

        # Always display a preview of the Excel file
        _display_file_preview_stage1(state)

    except Exception as e:
        debug(f"Error processing file: {e}")
        st.error(f"Error processing file: {e}")

def _display_processing_results_stage1(state, uploaded_file, file_content, temp_file_path):
    """Display the results of file processing in Stage 1."""
    st.markdown("**Processing Results:**")
    with st.container():
        # Verify the file was saved correctly
        if os.path.exists(temp_file_path) and os.path.getsize(temp_file_path) > 0:
            st.success(f"File uploaded: {uploaded_file.name}")

            # Parse the excel file using the cached function
            if parse_excel:
                try:
                    old_test_cases_count = safe_get_test_case_count(getattr(state, 'test_cases', None))
                    state.test_cases = parse_excel_cached(file_content)
                    new_test_cases_count = safe_get_test_case_count(state.test_cases)
                    debug(f"State change: test_cases count = {new_test_cases_count} (was: {old_test_cases_count})")

                    if new_test_cases_count == 0:
                        st.warning("No test cases found. Check file format.")
                    else:
                        st.success(f"Parsed {new_test_cases_count} test cases")

                        # Show progression button to Stage 2
                        st.markdown("---")
                        if st.button("Continue to Website Configuration", type="primary", key="continue_to_stage2"):
                            success = state.advance_to(StateStage.STAGE2_WEBSITE, "User completed CSV upload and chose to continue")
                            if success:
                                st.session_state['stage_progression_message'] = "Test cases loaded successfully. Please configure your website URL."
                                st.rerun()
                                return
                            else:
                                st.error("Failed to advance to Stage 2. Please try again.")

                except Exception as e:
                    debug(f"Error parsing file: {e}")
                    st.error(f"Error parsing file: {e}")
                    state.test_cases = None  # Ensure it's reset on error
            else:
                st.warning("Excel parsing function not available")
        else:
            st.error("Failed to save file")

def _display_file_preview_stage1(state):
    """Display a preview of the uploaded Excel file in Stage 1."""
    if hasattr(state, 'uploaded_excel') and os.path.exists(state.uploaded_excel):
        try:
            df = pd.read_excel(state.uploaded_excel)

            st.markdown("**File Preview:**")
            with st.container():
                # Show essential metric prominently
                if hasattr(state, 'test_cases'):
                    test_case_count = safe_get_test_case_count(state.test_cases)
                    if test_case_count > 0:
                        st.success(f"**{test_case_count} test cases** successfully parsed")
                    else:
                        st.warning("No test cases found in file")

                # Clean, focused data preview with toggle
                show_data_preview = st.checkbox("Show Data Preview", value=True, key="show_data_preview")
                if show_data_preview:
                    st.caption("First 10 rows:")
                    st.dataframe(df.head(10), use_container_width=True, hide_index=True)
        except Exception as e:
            debug(f"Error reading file for preview: {e}")
            st.error(f"Error reading file: {e}")

def _show_current_status_stage1(state):
    """Show current status if test cases are already loaded in Stage 1."""
    if hasattr(state, 'test_cases') and state.test_cases:
        test_case_count = safe_get_test_case_count(state.test_cases)
        if test_case_count > 0:
            st.success(f"**{test_case_count} test cases** already loaded from previous session")

            # Show progression button to Stage 2
            st.markdown("---")
            if st.button("Continue to Website Configuration", type="primary", key="continue_to_stage2_existing"):
                success = state.advance_to(StateStage.STAGE2_WEBSITE, "User chose to continue with existing test cases")
                if success:
                    st.session_state['stage_progression_message'] = "Continuing with existing test cases. Please configure your website URL."
                    st.rerun()
                    return
                else:
                    st.error("Failed to advance to Stage 2. Please try again.")

def _render_navigation_hub(state):
    """Render the navigation hub with mode selection."""
    # Professional welcome section with enterprise styling
    st.markdown("""
    <div style="
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    ">
        <h3 style="
            color: #374151;
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 0;
            margin-bottom: 0.75rem;
            letter-spacing: -0.025em;
        ">
            Workflow Selection
        </h3>
        <p style="
            color: #6b7280;
            margin-bottom: 0;
            font-size: 1rem;
            line-height: 1.5;
        ">
            Select your preferred workflow mode to begin automated test script generation.
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Mode selection with prominent buttons
    _render_mode_selection(state)

    st.markdown("---")

def _render_mode_selection(state):
    """Render mode selection buttons."""
    col1, col2 = st.columns(2)

    with col1:
        # Script Generation Mode Card
        st.markdown("""
        <div style="
            position: relative;
            border: 2px solid #2563eb;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease-in-out;
        ">
            <h4 style="
                color: #1e40af;
                font-size: 1.125rem;
                font-weight: 600;
                margin-top: 0;
                margin-bottom: 0.75rem;
                letter-spacing: -0.025em;
            ">
                🔧 Script Generation Mode
            </h4>
            <p style="
                color: #374151;
                margin-bottom: 0;
                font-weight: 400;
            ">
                Complete end-to-end workflow for creating new test scripts from CSV/Excel files with automated UI detection and optimization.
            </p>
            <div style="
                position: absolute;
                bottom: 1rem;
                right: 1rem;
                width: 8px;
                height: 8px;
                background-color: #2563eb;
                border-radius: 50%;
                opacity: 0.3;
            "></div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("<div style='margin-bottom: 1rem;'></div>", unsafe_allow_html=True)

        if st.button("Start Script Generation", key="start_script_generation", type="primary", use_container_width=True):
            # Navigate to Stage 2 to begin sequential workflow
            success = state.advance_to(StateStage.STAGE2_WEBSITE, "User selected Script Generation Mode from Stage 1")
            if success:
                st.session_state['stage_progression_message'] = "Entering Script Generation Mode. Please configure your website URL."
                st.rerun()
                return
            else:
                st.error("Failed to enter Script Generation Mode. Please try again.")

    with col2:
        # Script Playground Mode Card
        st.markdown("""
        <div style="
            position: relative;
            border: 2px solid #059669;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ecfdf5 0%, #ffffff 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease-in-out;
        ">
            <h4 style="
                color: #047857;
                font-size: 1.125rem;
                font-weight: 600;
                margin-top: 0;
                margin-bottom: 0.75rem;
                letter-spacing: -0.025em;
            ">
                🎮 Script Playground Mode
            </h4>
            <p style="
                color: #374151;
                margin-bottom: 0;
                font-weight: 400;
            ">
                Work with existing optimized scripts as templates to generate new automation scripts using proven patterns and best practices.
            </p>
            <div style="
                position: absolute;
                bottom: 1rem;
                right: 1rem;
                width: 8px;
                height: 8px;
                background-color: #059669;
                border-radius: 50%;
                opacity: 0.3;
            "></div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("<div style='margin-bottom: 1rem;'></div>", unsafe_allow_html=True)

        if st.button("Enter Script Playground", key="enter_script_playground", type="primary", use_container_width=True):
            # Navigate to Stage 10 for template management
            success = state.advance_to(StateStage.STAGE10_PLAYGROUND, "User selected Script Playground Mode from Stage 1")
            if success:
                st.session_state['stage_progression_message'] = "Entering Script Playground Mode. Select templates and test cases to begin."
                st.rerun()
                return
            else:
                st.error("Failed to enter Script Playground Mode. Please try again.")

def _render_file_upload_section(state):
    """Render the file upload section for Script Generation mode."""
    # Show file upload section with professional styling
    st.markdown("""
    <div style="margin-bottom: 1.5rem;">
        <h2 style="color: #1f2937; font-size: 1.875rem; font-weight: 600; margin-bottom: 0.5rem;">
            Upload Test Cases
        </h2>
        <p style="color: #6b7280; font-size: 1rem; margin-bottom: 0;">
            Upload your Excel file containing test cases to begin the script generation workflow.
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Help text about Excel format requirements
    with st.expander("Excel Format Requirements", expanded=False):
        st.info("""
        **Required Excel Columns:**
        - **Test Case ID**: Unique identifier for each test case
        - **Test Case Objective**: Description of what is being tested
        - **Step No**: Sequential step number within the test case
        - **Test Steps**: Detailed action to perform
        - **Expected Result**: Expected outcome of the step
        """)

    # Main file upload section
    st.markdown("### Upload Test Case File")
    uploaded_file = st.file_uploader(
        "Select your Excel file containing test cases (.xlsx)",
        type=["xlsx"],
        key="excel_uploader",
        help="Upload an Excel file with test cases following the required format"
    )

    # Process uploaded file
    if uploaded_file is not None:
        _process_uploaded_file_stage1(state, uploaded_file)
    else:
        # Show current status if test cases are already loaded
        _show_current_status_stage1(state)

    # Optional test case file upload preparation section
    _render_optional_file_preparation(state)

def _render_optional_file_preparation(state):
    """Render optional file preparation section."""
    # Optional file preparation section (collapsed by default)
    with st.expander("📁 Optional: Prepare Test Case File", expanded=False):
        st.markdown("*You can optionally upload and preview test case files here before selecting a workflow mode.*")

        # Simple file uploader for preparation
        prep_file = st.file_uploader(
            "Upload Excel file for preview",
            type=["xlsx"],
            key="prep_file_uploader",
            help="Upload to preview test cases before choosing workflow mode"
        )

        if prep_file is not None:
            try:
                # Save and process the file for preview
                file_content = prep_file.getvalue()

                # Validate the file
                validation_errors = validate_uploaded_file(prep_file, file_content)
                if validation_errors:
                    for error in validation_errors:
                        st.error(f"❌ {error}")
                    return

                # Parse for preview
                temp_dir = Path("temp_uploads")
                temp_dir.mkdir(exist_ok=True)
                safe_filename = ''.join(c if c.isalnum() else '_' for c in prep_file.name)
                temp_file_path = temp_dir / f"prep_{safe_filename}"

                with open(temp_file_path, "wb") as f:
                    f.write(file_content)

                # Parse test cases for preview
                if parse_excel:
                    try:
                        preview_test_cases = parse_excel_cached(file_content)
                        new_test_cases_count = safe_get_test_case_count(preview_test_cases)

                        if new_test_cases_count == 0:
                            st.warning("No test cases found. Check file format.")
                        else:
                            st.success(f"Parsed {new_test_cases_count} test cases")
                            st.info("Test cases loaded successfully! Use the navigation buttons above to choose your workflow mode.")

                            # Store in state for later use
                            state.test_cases = preview_test_cases
                            state.uploaded_excel = str(temp_file_path)
                            state.uploaded_file = str(temp_file_path)

                            # Show preview
                            _display_file_preview_stage1(state)

                    except Exception as e:
                        debug(f"Error parsing prep file: {e}")
                        st.error(f"Error parsing file: {e}")
                else:
                    st.warning("Excel parsing function not available")

            except Exception as e:
                debug(f"Error processing prep file: {e}")
                st.error(f"Error processing file: {e}")

        # Show current status if test cases are already loaded
        elif hasattr(state, 'test_cases') and state.test_cases:
            test_case_count = safe_get_test_case_count(state.test_cases)
            if test_case_count > 0:
                st.success(f"**{test_case_count} test cases** already loaded from previous session")
                st.info("You can proceed with either workflow mode using the buttons above.")
